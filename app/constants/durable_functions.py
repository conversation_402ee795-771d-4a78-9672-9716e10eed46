from enum import StrEnum


__all__ = [
    'OrchestratorName',
    'ActivityName',
    'ProcessingStatus',
    'EventType',
    'ExtractStatus',
    'OrchestratorInputType',
    'EntityOperation',
]


class OrchestratorName(StrEnum):
    DocumentProcessing = 'DocumentProcessingOrchestrator'
    UnifiedProcessing = 'UnifiedProcessingOrchestrator'
    EnhancedExtraction = 'EnhancedExtractionOrchestrator'
    EnhancedProcessingSubOrchestrator = 'EnhancedProcessingSubOrchestrator'


class ActivityName(StrEnum):
    ExtractDocumentText = 'ExtractDocumentTextActivity'
    ChunkDocument = 'ChunkDocumentActivity'
    UpdateProcessingStatus = 'UpdateProcessingStatusActivity'
    SendNotification = 'SendNotificationActivity'
    SendQueueMessage = 'SendQueueMessageActivity'
    ReadPrompt = 'ReadPromptActivity'
    DetectDocumentLanguage = 'DetectDocumentLanguageActivity'
    ExtractData = 'ExtractDataActivity'
    MergeExtractionData = 'MergeExtractionDataActivity'
    SaveExtractionData = 'SaveExtractionDataActivity'
    AggregateMultiSourceData = 'AggregateMultiSourceDataActivity'
    SaveAggregatedResultsToBlob = 'SaveAggregatedResultsToBlobActivity'
    SendFinalQueueMessage = 'SendFinalQueueMessageActivity'
    TranslateExtractedData = 'TranslateExtractedDataActivity'
    ExtractConflictsDetails = 'ExtractConflictsDetailsActivity'
    SaveConflicts = 'SaveConflictsActivity'
    SaveExtractionResultsToBlob = 'SaveExtractionResultsToBlobActivity'
    TranslateText = 'TranslateTextActivity'
    ListBlobs = 'ListBlobsActivity'
    EnhancedExtraction = 'EnhancedExtractionActivity'
    LoadProjectRoles = 'LoadProjectRolesActivity'
    LoadClientIndustries = 'LoadClientIndustriesActivity'
    LoadClientServices = 'LoadClientServicesActivity'
    LoadEngagementLocations = 'LoadEngagementLocationsActivity'


class ProcessingStatus(StrEnum):
    DocumentExtractionStarted = 'DocumentExtractionStarted'
    DocumentIsCorrupted = 'DocumentIsCorrupted'
    DocumentExtractionFailed = 'DocumentExtractionFailed'
    DocumentExtractionCompleted = 'DocumentExtractionCompleted'
    DocumentChunkingCompleted = 'DocumentChunkingCompleted'
    DocumentRequiredFieldsExtracted = 'DocumentRequiredFieldsExtracted'

    DocumentProcessingFailed = 'DocumentProcessingFailed'

    PromptProcessingStarted = 'PromptProcessingStarted'
    PromptProcessingFailed = 'PromptProcessingFailed'
    PromptProcessingCompleted = 'PromptProcessingCompleted'
    PromptChunkingCompleted = 'PromptChunkingCompleted'
    PromptRequiredFieldsExtracted = 'PromptRequiredFieldsExtracted'

    OrchestratorDocumentProcessingFailed = 'OrchestratorDocumentProcessingFailed'
    OrchestratorDocumentProcessingCompleted = 'OrchestratorDocumentProcessingCompleted'

    # Unified processing statuses
    UnifiedProcessingStarted = 'UnifiedProcessingStarted'
    UnifiedProcessingCompleted = 'UnifiedProcessingCompleted'
    UnifiedProcessingFailed = 'UnifiedProcessingFailed'
    MultiSourceDataAggregated = 'MultiSourceDataAggregated'
    FinalResultsSavedToBlob = 'FinalResultsSavedToBlob'

    ConfirmedFieldsChangeProhibited = 'ConfirmedFieldsChangeProhibited'
    DateConfirmationNeeded = 'DateConfirmationNeeded'


class EventType(StrEnum):
    DocumentExtractionStarted = 'document_extraction_started'
    DocumentExtractionFailed = 'document_extraction_failed'
    DocumentChunkingCompleted = 'document_chunking_completed'

    PromptProcessingStarted = 'prompt_processing_started'
    PromptProcessingFailed = 'prompt_processing_failed'
    PromptProcessingCompleted = 'prompt_processing_completed'
    PromptChunkingCompleted = 'prompt_chunking_completed'

    RequiredFieldsExtracted = 'required_fields_extracted'  # single event for docs and prompts

    DocumentProcessingFailed = 'document_processing_failed'
    DocumentIsCorruptedError = 'document_is_corrupted_error'
    DocumentsAreCorruptedError = 'documents_are_corrupted_error'

    # Unified processing events
    UnifiedProcessingStarted = 'unified_processing_started'
    UnifiedProcessingCompleted = 'unified_processing_completed'
    UnifiedProcessingFailed = 'unified_processing_failed'
    UnifiedProcessingError = 'unified_processing_error'  # When all files in batch are corrupted
    MultiSourceDataAggregated = 'multi_source_data_aggregated'

    # Enhanced processing events
    DraftQualProgress = 'draft_qual_progress'
    DateConfirmationNeeded = 'date_confirmation_needed'


class ExtractStatus(StrEnum):
    Success = 'success'
    Failed = 'failed'


class OrchestratorInputType(StrEnum):
    Document = 'document'
    Prompt = 'text-prompt'
    Unified = 'unified'


class EntityOperation(StrEnum):
    """Entity operation names for Durable Entities."""

    INITIALIZE = 'initialize'
    INCREMENT = 'increment'
    RESET = 'reset'
