"""
Tests for confirmed fields replacement bug fix.

Tests the business logic for handling confirmed field changes in document processing.
See docs/confirmed-fields-business-logic.md for detailed business rules.
"""

from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4

import pytest

from constants.extracted_data import ConfirmedD<PERSON><PERSON>ields, ConversationState, RequiredField
from constants.message import MessageRole, SystemReplyType
from schemas import ConfirmedData, NewConfirmedDataForUpdate
from services.message_handlers.prompt_handler import Prompt<PERSON><PERSON><PERSON><PERSON>and<PERSON>
from validators.extracted_data import check_confirmed_fields_replacement


class TestProhibitedFieldsConfiguration:
    """Test prohibited fields configuration."""

    def test_outcomes_in_prohibited_list(self):
        """Test OUTCOMES field is prohibited."""
        assert ConfirmedDataFields.OUTCOMES.is_prohibited_from_replacement

    def test_objective_and_scope_in_prohibited_list(self):
        """Test OBJECTIVE_AND_SCOPE field is prohibited."""
        assert ConfirmedDataFields.OBJECTIVE_AND_SCOPE.is_prohibited_from_replacement

    def test_client_name_still_prohibited(self):
        """Test CLIENT_NAME field is prohibited."""
        assert ConfirmedDataFields.CLIENT_NAME.is_prohibited_from_replacement

    def test_ldmf_country_still_prohibited(self):
        """Test LDMF_COUNTRY field is prohibited."""
        assert ConfirmedDataFields.LDMF_COUNTRY.is_prohibited_from_replacement

    def test_date_intervals_not_prohibited(self):
        """Test DATE_INTERVALS field is not prohibited."""
        assert not ConfirmedDataFields.DATE_INTERVALS.is_prohibited_from_replacement


class TestConfirmedFieldsReplacementValidation:
    """Test validation logic for confirmed field replacement."""

    def test_outcomes_replacement_allowed(self):
        """Test outcomes replacement is allowed."""
        current_confirmed_data = ConfirmedData(
            client_name='Test Client', ldmf_country='Test Country', outcomes='Original outcomes'
        )

        new_data = NewConfirmedDataForUpdate(client_names=[], ldmf_countries=[], outcomes='Updated outcomes')

        replaced_fields = check_confirmed_fields_replacement(
            current_confirmed_data=current_confirmed_data, new_confirmed_data_to_update=new_data
        )

        assert ConfirmedDataFields.OUTCOMES not in replaced_fields

    def test_objective_and_scope_replacement_allowed(self):
        """Test objective and scope replacement is allowed."""
        current_confirmed_data = ConfirmedData(
            client_name='Test Client', ldmf_country='Test Country', objective_and_scope='Original objective'
        )

        new_data = NewConfirmedDataForUpdate(
            client_names=[], ldmf_countries=[], objective_and_scope='Updated objective'
        )

        replaced_fields = check_confirmed_fields_replacement(
            current_confirmed_data=current_confirmed_data, new_confirmed_data_to_update=new_data
        )

        assert ConfirmedDataFields.OBJECTIVE_AND_SCOPE not in replaced_fields

    def test_client_name_replacement_still_prohibited(self):
        """Test that client name replacement is still prohibited."""
        current_confirmed_data = ConfirmedData(client_name='Original Client')

        new_data = NewConfirmedDataForUpdate(client_names=['Updated Client'], ldmf_countries=[])

        replaced_fields = check_confirmed_fields_replacement(
            current_confirmed_data=current_confirmed_data, new_confirmed_data_to_update=new_data
        )

        assert ConfirmedDataFields.CLIENT_NAME in replaced_fields

    def test_ldmf_country_replacement_still_prohibited(self):
        """Test that LDMF country replacement is still prohibited."""
        current_confirmed_data = ConfirmedData(ldmf_country='Original Country')

        new_data = NewConfirmedDataForUpdate(client_names=[], ldmf_countries=['Updated Country'])

        replaced_fields = check_confirmed_fields_replacement(
            current_confirmed_data=current_confirmed_data, new_confirmed_data_to_update=new_data
        )

        assert ConfirmedDataFields.LDMF_COUNTRY in replaced_fields

    def test_multiple_field_replacement_mixed_scenario(self):
        """Test scenario where some fields are allowed and some are prohibited."""
        current_confirmed_data = ConfirmedData(
            client_name='Original Client',
            ldmf_country='Original Country',
            objective_and_scope='Original objective',
            outcomes='Original outcomes',
        )

        new_data = NewConfirmedDataForUpdate(
            client_names=['Updated Client'],  # Should be prohibited
            ldmf_countries=['Updated Country'],  # Should be prohibited
            objective_and_scope='Updated objective',  # Should be allowed
            outcomes='Updated outcomes',  # Should be allowed
        )

        replaced_fields = check_confirmed_fields_replacement(
            current_confirmed_data=current_confirmed_data, new_confirmed_data_to_update=new_data
        )

        assert ConfirmedDataFields.CLIENT_NAME in replaced_fields
        assert ConfirmedDataFields.LDMF_COUNTRY in replaced_fields
        assert ConfirmedDataFields.OBJECTIVE_AND_SCOPE not in replaced_fields
        assert ConfirmedDataFields.OUTCOMES not in replaced_fields
        assert len(replaced_fields) == 2


@pytest.mark.asyncio
class TestConfirmedFieldsChangeMessageHandler:
    """Test message handler for confirmed fields change scenarios."""

    @pytest.fixture
    def mock_prompt_handler(self):
        """Create a mock prompt handler with necessary dependencies."""

        handler = PromptMessageHandler(
            conflict_service=AsyncMock(),
            conversation_message_repository=AsyncMock(),
            conversation_repository=AsyncMock(),
            document_service=AsyncMock(),
            document_db_repository=AsyncMock(),
            processing_message_repository=AsyncMock(),
            kx_dash_service=AsyncMock(),
            translation_service=AsyncMock(),
            extracted_data_service=AsyncMock(),
            date_validator_service=AsyncMock(),
            system_message_generation_service=AsyncMock(),
            client_industry_service=AsyncMock(),
            client_service_service=AsyncMock(),
            engagement_location_service=AsyncMock(),
            openai_repository=AsyncMock(),
            client_name_option_handler=AsyncMock(),
            conflict_option_handler=AsyncMock(),
            ldmf_country_option_handler=AsyncMock(),
            dates_option_handler=AsyncMock(),
            kx_dash_task_option_handler=AsyncMock(),
            openai_client_name_validation_service=AsyncMock(),
        )
        return handler

    async def test_data_complete_state_uses_original_logic(self, mock_prompt_handler):
        """Test that DATA_COMPLETE state still uses the original prohibited message logic."""
        conversation_id = uuid4()
        token = 'test_token'

        # Mock conversation data with DATA_COMPLETE state
        mock_conversation_data = MagicMock()
        mock_conversation_data.conversation.State = ConversationState.DATA_COMPLETE
        mock_conversation_data.confirmed_data = ConfirmedData()
        mock_conversation_data.aggregated_data = MagicMock()

        # Mock ProactiveChatService to return no proactive message (to avoid RuntimeError)
        with patch('services.message_handlers.prompt_handler.ProactiveChatService') as mock_proactive_service_class:
            mock_proactive_service = MagicMock()
            mock_proactive_service.get_proactive_system_message.return_value = (None, None)  # No proactive message
            mock_proactive_service_class.return_value = mock_proactive_service

            mock_prompt_handler._fetch_conversation_data = AsyncMock(return_value=mock_conversation_data)
            mock_prompt_handler.conversation_message_repository.create = AsyncMock()

            # Call the method
            await mock_prompt_handler._handle_confirmed_fields_change_prohibited_message(conversation_id, token)

            # Should not call get_missing_required_data_prompts for DATA_COMPLETE state
            mock_prompt_handler.extracted_data_service.get_missing_required_data_prompts.assert_not_called()

            # Should create message with prohibited content
            mock_prompt_handler.conversation_message_repository.create.assert_called_once()
            call_args = mock_prompt_handler.conversation_message_repository.create.call_args[0][0]
            assert SystemReplyType.CONFIRMED_FIELDS_CHANGE_PROHIBITED.message_text in call_args.content

    async def test_non_data_complete_state_uses_confirmation_flow(self, mock_prompt_handler):
        """Test that non-DATA_COMPLETE states use the confirmation flow."""
        conversation_id = uuid4()
        token = 'test_token'

        # Mock conversation data with non-DATA_COMPLETE state
        mock_conversation_data = MagicMock()
        mock_conversation_data.conversation.State = ConversationState.COLLECTING_OUTCOMES
        mock_conversation_data.confirmed_data = ConfirmedData()
        mock_conversation_data.aggregated_data = MagicMock()
        mock_conversation_data.aggregated_data.ldmf_countries_as_options = []
        mock_conversation_data.aggregated_data.date_intervals = []

        # Mock ProactiveChatService
        mock_proactive_service = MagicMock()
        mock_proactive_service.get_proactive_system_message.return_value = (
            'Please confirm outcomes',
            SystemReplyType.OUTCOMES_CONFIRMED,
        )
        mock_proactive_service.next_required_field = RequiredField.OUTCOMES

        mock_prompt_handler._fetch_conversation_data = AsyncMock(return_value=mock_conversation_data)
        mock_prompt_handler.conversation_message_repository.create = AsyncMock()

        # Mock ProactiveChatService constructor
        with patch(
            'services.message_handlers.prompt_handler.ProactiveChatService', return_value=mock_proactive_service
        ):
            # Call the method
            await mock_prompt_handler._handle_confirmed_fields_change_prohibited_message(conversation_id, token)

        # Should create a system message for non-DATA_COMPLETE state
        mock_prompt_handler.conversation_message_repository.create.assert_called_once()

        # Verify the message was created with the expected content
        call_args = mock_prompt_handler.conversation_message_repository.create.call_args[0][0]
        assert call_args.conversation_id == conversation_id
        assert call_args.role == MessageRole.SYSTEM
        assert call_args.system_reply_type == SystemReplyType.OUTCOMES_CONFIRMED

        # Should create message with confirmation content (includes base message + proactive message)
        expected_content = (
            "Sorry, it's not possible to update confirmed information at this stage. "
            'However, you can make changes to any information (except client name) once the draft qual is created. '
            'Alternatively, you may delete this qual and create a new one if needed.\n\n'
            'Please confirm outcomes'
        )
        assert call_args.content == expected_content
        assert call_args.system_reply_type == SystemReplyType.OUTCOMES_CONFIRMED


@pytest.mark.asyncio
class TestDocumentProcessingIntegration:
    """Integration tests for document processing."""

    async def test_outcomes_and_objective_updates_allowed_in_document_processing(self):
        """Test that outcomes and objective updates are allowed in document processing."""
        # This test would require mocking the entire document processing activity
        # For now, we test the core validation logic that's used by document processing

        current_confirmed_data = ConfirmedData(
            client_name='Test Client',
            ldmf_country='Test Country',
            objective_and_scope='Original objective',
            outcomes='Original outcomes',
        )

        # Simulate second document trying to update outcomes and objective
        new_data = NewConfirmedDataForUpdate(
            client_names=[],  # Not trying to update client name
            ldmf_countries=[],  # Not trying to update country
            objective_and_scope='Updated objective from second document',
            outcomes='Updated outcomes from second document',
        )

        replaced_fields = check_confirmed_fields_replacement(
            current_confirmed_data=current_confirmed_data, new_confirmed_data_to_update=new_data
        )

        # Should not flag any fields as being replaced (all updates are allowed)
        assert len(replaced_fields) == 0

    async def test_prohibited_fields_still_blocked_in_document_processing(self):
        """Test that prohibited fields are still blocked in document processing."""
        current_confirmed_data = ConfirmedData(client_name='Original Client', ldmf_country='Original Country')

        # Simulate second document trying to update prohibited fields
        new_data = NewConfirmedDataForUpdate(
            client_names=['Updated Client from second document'],
            ldmf_countries=['Updated Country from second document'],
        )

        replaced_fields = check_confirmed_fields_replacement(
            current_confirmed_data=current_confirmed_data, new_confirmed_data_to_update=new_data
        )

        # Should flag both prohibited fields
        assert ConfirmedDataFields.CLIENT_NAME in replaced_fields
        assert ConfirmedDataFields.LDMF_COUNTRY in replaced_fields
        assert len(replaced_fields) == 2

    async def test_mixed_scenario_document_processing(self):
        """Test mixed scenario where some updates are allowed and some are blocked."""
        current_confirmed_data = ConfirmedData(
            client_name='Original Client',
            ldmf_country='Original Country',
            objective_and_scope='Original objective',
            outcomes='Original outcomes',
        )

        # Simulate second document with mixed updates
        new_data = NewConfirmedDataForUpdate(
            client_names=['Updated Client'],  # Should be blocked
            ldmf_countries=[],  # Not updating
            objective_and_scope='Updated objective',  # Should be allowed
            outcomes='Updated outcomes',  # Should be allowed
        )

        replaced_fields = check_confirmed_fields_replacement(
            current_confirmed_data=current_confirmed_data, new_confirmed_data_to_update=new_data
        )

        # Should only flag the prohibited field
        assert ConfirmedDataFields.CLIENT_NAME in replaced_fields
        assert ConfirmedDataFields.LDMF_COUNTRY not in replaced_fields
        assert ConfirmedDataFields.OBJECTIVE_AND_SCOPE not in replaced_fields
        assert ConfirmedDataFields.OUTCOMES not in replaced_fields
        assert len(replaced_fields) == 1


class TestDateIntervalHandling:
    """Test date interval handling."""

    def test_date_intervals_not_in_prohibited_list(self):
        """Test that date intervals are not in the prohibited fields list."""
        # Date intervals should be allowed to be updated
        assert not ConfirmedDataFields.DATE_INTERVALS.is_prohibited_from_replacement

    def test_date_intervals_not_checked_in_validation(self):
        """Test that date intervals are not checked in the validation function."""
        # The NewConfirmedDataForUpdate schema doesn't include date_intervals,
        # so they're not checked in check_confirmed_fields_replacement
        current_confirmed_data = ConfirmedData(date_intervals=('2023-01-01', '2023-12-31'))

        new_data = NewConfirmedDataForUpdate(client_names=[], ldmf_countries=[])

        replaced_fields = check_confirmed_fields_replacement(
            current_confirmed_data=current_confirmed_data, new_confirmed_data_to_update=new_data
        )

        # Should not flag any fields since date intervals aren't checked
        assert len(replaced_fields) == 0


class TestEndToEndScenarios:
    """Test end-to-end scenarios."""

    def test_first_document_all_fields_second_document_updates_allowed_fields(self):
        """Test the specific scenario from the bug report."""
        # First document has all 5 fields confirmed
        current_confirmed_data = ConfirmedData(
            client_name='Test Client',
            ldmf_country='Test Country',
            date_intervals=('2023-01-01', '2023-12-31'),
            objective_and_scope='Original objective',
            outcomes='Original outcomes',
        )

        # Second document tries to update date intervals, objective, and outcomes
        new_data = NewConfirmedDataForUpdate(
            client_names=[],  # Not updating
            ldmf_countries=[],  # Not updating
            objective_and_scope='Updated objective from second document',
            outcomes='Updated outcomes from second document',
        )

        replaced_fields = check_confirmed_fields_replacement(
            current_confirmed_data=current_confirmed_data, new_confirmed_data_to_update=new_data
        )

        # Should allow all updates (no fields flagged as replaced)
        assert len(replaced_fields) == 0

    def test_backward_compatibility_prohibited_fields_still_work(self):
        """Test that the existing prohibited field logic still works."""
        current_confirmed_data = ConfirmedData(client_name='Original Client', ldmf_country='Original Country')

        new_data = NewConfirmedDataForUpdate(client_names=['New Client'], ldmf_countries=['New Country'])

        replaced_fields = check_confirmed_fields_replacement(
            current_confirmed_data=current_confirmed_data, new_confirmed_data_to_update=new_data
        )

        # Should still block these updates
        assert ConfirmedDataFields.CLIENT_NAME in replaced_fields
        assert ConfirmedDataFields.LDMF_COUNTRY in replaced_fields
